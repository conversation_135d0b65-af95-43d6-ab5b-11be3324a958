# 新闻总结API

基于Ollama大模型的新闻标题生成服务，根据新闻内容智能生成简洁准确的标题，支持单条和批量新闻处理。

## 功能特点

- 🤖 基于本地Ollama大模型，保护数据隐私
- 📝 智能生成新闻标题，控制长度15-25字符
- 🚀 支持单条和批量新闻处理
- 🔄 支持多组新闻批量处理
- ⚡ 高性能异步处理
- 🛡️ 完善的错误处理和日志记录

## 环境要求

- Python 3.7+
- Ollama服务运行在 `http://127.0.0.1:11500`
- 已安装qwen3:0.6b模型

### 安装依赖

```bash
pip install requests flask
```

## 快速开始

### 1. 启动API服务

```bash
python news_push/news_summary_api.py
```

服务将在 `http://localhost:5000` 启动

### 2. 使用Python SDK

```python
from news_push.news_summarizer import create_news_summary_api

# 创建API实例
api = create_news_summary_api()

# 准备新闻数据
news_data = [{
    "content": "某公司今日正式发布了其最新研发的人工智能产品，该产品具有革命性的技术突破，预计将对整个行业产生深远影响...",
    "source": "科技日报"
}]

# 调用总结服务
result = api(news_data)
print(result["data"])  # 输出生成的标题
```

## API接口文档

### 健康检查

```http
GET /health
```

**响应示例:**
```json
{
  "status": "healthy",
  "service": "news-summarizer", 
  "version": "1.0.0"
}
```

### 新闻总结

```http
POST /summarize
Content-Type: application/json
```

**请求体:**
```json
{
  "news": [
    {
      "content": "新闻内容",
      "source": "新闻来源（可选）"
    }
  ]
}
```

**响应示例:**
```json
{
  "success": true,
  "data": ["生成的标题"],
  "message": "成功处理1条新闻",
  "count": 1
}
```

### 批量新闻总结

```http
POST /summarize/batch
Content-Type: application/json
```

**请求体:**
```json
{
  "batches": [
    {
      "id": "batch1",
      "news": [
        {
          "content": "内容1", 
          "source": "来源1（可选）"
        }
      ]
    }
  ]
}
```

**响应示例:**
```json
{
  "success": true,
  "results": [
    {
      "id": "batch1",
      "success": true,
      "data": ["优化标题1"],
      "message": "成功处理1条新闻",
      "count": 1
    }
  ],
  "total_batches": 1
}
```

## 使用示例

### cURL示例

```bash
# 单条新闻总结
curl -X POST http://localhost:5000/summarize \
  -H "Content-Type: application/json" \
  -d '{
    "news": [{
      "content": "某公司今日正式发布了其最新研发的人工智能产品，该产品具有革命性的技术突破...",
      "source": "科技日报"
    }]
  }'

# 批量处理
curl -X POST http://localhost:5000/summarize/batch \
  -H "Content-Type: application/json" \
  -d '{
    "batches": [
      {
        "id": "tech_news",
        "news": [{"content": "...", "source": "..."}]
      }
    ]
  }'
```

### Python requests示例

```python
import requests
import json

# 单条新闻
data = {
    "news": [{
        "content": "某公司今日正式发布了其最新研发的人工智能产品，该产品具有革命性的技术突破...",
        "source": "科技日报"
    }]
}

response = requests.post(
    "http://localhost:5000/summarize",
    json=data
)

result = response.json()
if result["success"]:
    print("生成标题:", result["data"][0])
```

## 测试

### 运行单元测试

```bash
python news_push/test_news_summarizer.py
```

### 运行API测试

```bash
# 先启动API服务
python news_push/news_summary_api.py

# 在另一个终端运行测试
python news_push/test_api.py
```

## 配置说明

### Ollama配置

默认配置:
- URL: `http://127.0.0.1:11500/api/generate`
- 模型: `qwen3:0.6b`

可以通过修改 `NewsSummarizer` 类的初始化参数来自定义:

```python
summarizer = NewsSummarizer(
    ollama_url="http://your-ollama-server:11434/api/generate",
    model="your-model-name"
)
```

### Prompt优化

系统使用精心设计的prompt来确保:
- 标题长度控制在15-25字符
- 突出新闻核心要点
- 保持客观中性语调
- 避免冗余词汇

## 错误处理

API提供完善的错误处理:

- `400 Bad Request`: 请求格式错误
- `500 Internal Server Error`: 服务器内部错误
- 自动降级: AI处理失败时返回原标题

## 性能优化

- 使用非流式响应减少解析复杂度
- 支持批量处理提高效率
- 合理的超时设置(30秒)
- 详细的日志记录便于调试

## 注意事项

1. 确保Ollama服务正常运行
2. 模型需要预先下载到本地
3. 处理大量新闻时建议使用批量接口
4. 建议在生产环境中配置适当的超时和重试机制

## 许可证

MIT License