# 新闻推送系统 - AI 标题生成功能

这是一个集成了 AI 标题生成功能的新闻推送系统，使用本地 Ollama 模型为新闻生成更直观明了的标题。

## 🚀 新功能特点

- **智能标题生成**: 使用本地 qwen:0.6b 模型生成直观的新闻标题
- **自动服务管理**: 自动检查和启动 Ollama 服务
- **降级机制**: AI 生成失败时自动使用默认标题格式
- **批量处理**: 支持为多条新闻生成统一的汇总标题
- **隐私保护**: 完全本地化处理，不依赖外部 API

## 📋 系统要求

- Python 3.7+
- Ollama (本地 AI 模型服务)
- 所需 Python 包: requests, beautifulsoup4, colorama

## 🛠️ 安装和配置

### 1. 安装 Ollama

```bash
# 访问 https://ollama.ai/ 下载安装
# 或使用包管理器安装
```

### 2. 安装 Python 依赖

```bash
pip install requests beautifulsoup4 colorama
```

### 3. 启动和配置 Ollama 服务

```bash
# 使用提供的管理脚本
python start_ollama.py
```

或手动操作：

```bash
# 启动 Ollama 服务 (端口 11500)
OLLAMA_HOST=127.0.0.1:11500 ollama serve

# 下载 qwen:0.6b 模型
ollama pull qwen:0.6b
```

## 🎯 使用方法

### 运行新闻推送系统

```bash
# 启用推送功能，15分钟推送一次
python jasper_batch_push_test.py 900

# 禁用推送功能，仅监控
python jasper_batch_push_test.py no-push
```

### 测试 AI 标题生成功能

```bash
# 测试 AI 功能是否正常
python test_ai_title.py
```

## 🤖 AI 标题生成说明

### 工作原理

1. **批量标题生成**: 为一批新闻生成统一的汇总标题
2. **智能摘要**: 分析新闻内容和来源，生成简洁标题
3. **长度控制**: 标题长度控制在 15-30 个字符
4. **降级处理**: AI 失败时使用基于来源的默认标题

### 标题生成示例

**原标题**: `快讯汇总 (3条) - 路透社/彭博社`

**AI 生成标题**: `美联储政策与科技股财报要闻 (3条)`

### 配置选项

在 `news_summarizer.py` 中可以调整：

```python
# 模型配置
model = "qwen:0.6b"  # 可改为其他模型
ollama_url = "http://127.0.0.1:11500/api/generate"  # 服务地址
```

## 📁 文件说明

- `jasper_batch_push_test.py`: 主要的新闻推送脚本
- `news_summarizer.py`: AI 标题生成模块
- `start_ollama.py`: Ollama 服务管理脚本
- `test_ai_title.py`: AI 功能测试脚本
- `ollama_test.py`: 基础 Ollama 连接测试

## 🔧 故障排除

### 常见问题

1. **AI 标题生成失败**
   - 检查 Ollama 服务是否运行: `python start_ollama.py`
   - 确认模型是否下载: `ollama list`

2. **服务启动失败**
   - 检查端口 11500 是否被占用
   - 确认 Ollama 是否正确安装

3. **模型下载慢**
   - 模型大小约 400MB，首次下载需要时间
   - 可以手动下载: `ollama pull qwen:0.6b`

### 日志查看

```bash
# 查看推送日志
tail -f jasper_batch_monitor.log
```

## 🎨 自定义配置

### 修改模型

如果想使用其他模型，修改 `news_summarizer.py`:

```python
# 例如使用更大的模型
model = "qwen:1.8b"  # 或 "llama2:7b"
```

### 调整标题生成策略

在 `_create_batch_title_prompt` 方法中可以调整 prompt 模板。

## 📊 性能说明

- **qwen:0.6b 模型**: 响应速度快，适合实时处理
- **内存占用**: 约 1-2GB
- **生成时间**: 通常 1-3 秒

## 🔄 更新日志

### v2.0 (当前版本)
- ✅ 集成 AI 标题生成功能
- ✅ 自动 Ollama 服务管理
- ✅ 批量新闻标题生成
- ✅ 智能降级机制

### v1.0
- ✅ 基础新闻推送功能
- ✅ 批量推送支持

## 📝 注意事项

1. **首次使用**
   - 需要下载约 400MB 的模型文件
   - 建议在网络良好时进行初始化

2. **性能考虑**
   - qwen:0.6b 模型响应速度快，适合实时处理
   - 如需更高质量，可考虑使用更大的模型

3. **错误处理**
   - 系统具有完善的降级机制
   - AI 生成失败时会自动使用默认标题格式

4. **隐私保护**
   - 所有处理完全在本地进行
   - 不会向外部服务发送任何数据

## 🤝 贡献

欢迎提交 Issue 和 Pull Request 来改进这个项目！

## 📄 许可证

MIT License