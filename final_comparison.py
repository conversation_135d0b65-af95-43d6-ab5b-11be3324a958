#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终对比测试：展示优化前后的标题生成效果
"""

from news_summarizer import NewsSummarizer
from colorama import init, Fore, Style

# 初始化colorama
init(autoreset=True)

def final_comparison_test():
    """最终对比测试"""
    print(f"{Fore.GREEN}🎯 新闻标题生成 - 最终效果对比")
    print("=" * 60)
    
    # 初始化
    summarizer = NewsSummarizer()
    
    # 真实新闻场景测试
    test_cases = [
        {
            "scenario": "金融政策新闻",
            "news": [
                {
                    "content": "中国人民银行今日宣布下调存款准备金率0.5个百分点，释放流动性约1万亿元。这是央行今年第二次降准操作，旨在支持实体经济发展。",
                    "source": "国内"
                },
                {
                    "content": "美联储宣布加息25个基点，将联邦基金利率上调至5.25%-5.5%区间。这是美联储今年第11次加息，鲍威尔表示将继续关注通胀数据。",
                    "source": "路透社"
                }
            ]
        },
        {
            "scenario": "科技股财报",
            "news": [
                {
                    "content": "苹果公司发布第四季度财报，营收894亿美元，同比下降1%。iPhone销量为4780万部，低于分析师预期的4850万部。",
                    "source": "彭博社"
                },
                {
                    "content": "微软公布季度业绩，云计算业务Azure收入增长29%，达到243亿美元。整体营收为565亿美元，超出市场预期。",
                    "source": "华尔街日报"
                }
            ]
        },
        {
            "scenario": "股市波动",
            "news": [
                {
                    "content": "A股三大指数集体收涨，上证指数涨1.8%报3142点，深证成指涨2.3%，创业板指涨2.7%。两市成交额达8900亿元。",
                    "source": "国内"
                }
            ]
        },
        {
            "scenario": "国际贸易",
            "news": [
                {
                    "content": "商务部发布数据显示，前10个月我国进出口总值34.32万亿元，同比增长0.9%。其中出口19.7万亿元，进口14.62万亿元。",
                    "source": "国内"
                },
                {
                    "content": "欧盟宣布对中国电动汽车征收临时反补贴税，税率为17.4%-38.1%。此举将影响比亚迪、吉利等中国车企在欧洲的销售。",
                    "source": "路透社"
                }
            ]
        }
    ]
    
    for i, case in enumerate(test_cases, 1):
        print(f"\n{Fore.YELLOW}📊 测试场景 {i}: {case['scenario']}")
        print("-" * 50)
        
        news_list = case['news']
        
        # 显示新闻内容
        print(f"{Fore.CYAN}📰 新闻内容:")
        for j, news in enumerate(news_list, 1):
            print(f"  {j}. [{news['source']}] {news['content'][:70]}...")
        
        # 传统标题
        sources = list(set([news.get('source', '未知') for news in news_list]))
        traditional_title = f"快讯汇总 ({len(news_list)}条)"
        if len(sources) <= 3:
            traditional_title += f" - {'/'.join(sources)}"
        
        # AI 生成标题
        try:
            ai_title = summarizer.generate_batch_title(news_list)
            final_ai_title = f"{ai_title} ({len(news_list)}条)"
            
            print(f"\n{Fore.WHITE}📝 传统标题: {traditional_title}")
            print(f"{Fore.GREEN}🤖 AI 优化标题: {final_ai_title}")
            
            # 效果分析
            print(f"\n{Fore.MAGENTA}📈 优化效果分析:")
            
            # 信息量对比
            traditional_info = len([word for word in traditional_title if word not in "快讯汇总条-"])
            ai_info = len([char for char in ai_title if char.isalnum() or char in "%.万亿元美元"])
            
            print(f"  • 信息密度: 传统标题 vs AI标题")
            print(f"    - 传统: 主要显示来源信息")
            print(f"    - AI: 包含具体事件和数据")
            
            # 具体性分析
            if any(char.isdigit() for char in ai_title):
                print(f"  ✅ 包含具体数字/数据")
            if any(keyword in ai_title for keyword in ["央行", "美联储", "苹果", "微软", "上证", "欧盟"]):
                print(f"  ✅ 明确标识关键机构")
            if any(keyword in ai_title for keyword in ["降准", "加息", "财报", "收涨", "征收"]):
                print(f"  ✅ 突出核心事件")
            
            # 可读性评估
            if len(ai_title) <= 30:
                print(f"  ✅ 长度适中，易于阅读")
            
        except Exception as e:
            print(f"{Fore.RED}❌ AI 标题生成失败: {e}")
    
    # 总结
    print(f"\n{Fore.GREEN}🎉 对比测试完成！")
    print(f"\n{Fore.CYAN}💡 优化成果总结:")
    print(f"  🎯 {Fore.GREEN}信息量提升{Style.RESET_ALL}: 从通用描述到具体事件")
    print(f"  📊 {Fore.GREEN}数据突出{Style.RESET_ALL}: 优先显示关键数字和百分比")
    print(f"  🏢 {Fore.GREEN}主体明确{Style.RESET_ALL}: 清晰标识涉及的机构和公司")
    print(f"  ⚡ {Fore.GREEN}一目了然{Style.RESET_ALL}: 读者可快速了解新闻核心内容")
    print(f"  🔄 {Fore.GREEN}智能降级{Style.RESET_ALL}: AI失败时自动使用传统格式")
    
    print(f"\n{Fore.YELLOW}📋 使用建议:")
    print(f"  • 适用于金融、科技、政策等专业新闻")
    print(f"  • 特别适合包含数据和具体事件的新闻")
    print(f"  • 推送频率建议: 30分钟-1小时一次")
    print(f"  • 建议批量处理2-5条相关新闻")

if __name__ == "__main__":
    final_comparison_test()
