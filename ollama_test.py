import requests
import json

url = "http://127.0.0.1:11500/api/generate"
payload = {
    "model": "qwen3:0.6b",
    "prompt": "你好，介绍一下自己",
    "stream": False  # 禁用流式输出
}
resp = requests.post(url, json=payload)

# 检查响应状态
if resp.status_code == 200:
    try:
        result = resp.json()
        print("Response:", result)
        # 如果有response字段，打印生成的文本
        if 'response' in result:
            print("Generated text:", result['response'])
    except json.JSONDecodeError:
        print("Raw response text:")
        print(resp.text)
        # 尝试解析流式响应
        lines = resp.text.strip().split('\n')
        full_response = ""
        for line in lines:
            if line.strip():
                try:
                    data = json.loads(line)
                    if 'response' in data:
                        full_response += data['response']
                except json.JSONDecodeError:
                    continue
        if full_response:
            print("Parsed response:", full_response)
else:
    print(f"Error: HTTP {resp.status_code}")
    print(resp.text)
