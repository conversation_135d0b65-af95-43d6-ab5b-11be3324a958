#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Jasper 快讯监控脚本 - 批量推送版本
监控 https://jasper.lxaa.top/ 网站的快讯
支持批量推送功能：当监控间隔>=60秒时，收集所有符合条件的新闻后一次性推送
"""

import requests
import time
import json
from datetime import datetime
from bs4 import BeautifulSoup
import re
from colorama import init, Fore, Back, Style
import sys
import logging
import hashlib
from news_summarizer import NewsSummarizer

# 初始化colorama
init(autoreset=True)

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('jasper_batch_monitor.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

class JasperBatchMonitor:
    def __init__(self, enable_push=True):
        self.url = "https://jasper.lxaa.top/"
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'none',
            'Cache-Control': 'no-cache'
        })
        self.seen_news = set()
        self.enable_push = enable_push
        
        # 批量推送相关
        self.pending_push_news = []  # 待推送的新闻列表
        self.batch_push_threshold = 60  # 批量推送阈值（秒）
        self.last_save_time = time.time()  # 上次保存时间
        self.save_interval = 300  # 定期保存间隔（5分钟）
        self.last_push_time = time.time()  # 上次推送时间
        self.fetch_interval = 30  # 固定的获取新闻间隔（10秒）
        
        # 推送相关配置
        self.wechat_api_url = "http://pushplus.hxtrip.com/send"
        self.wechat_token = "5d652208bc684a61a4e90d657c7e6e3d"
        self.sent_news_file = "jasper_sent_news.json"
        self.sent_news = self.load_sent_news()
        
        # 推送过滤条件
        self.push_filters = [
            ['很重要', '路透社'],
            ['很重要', '彭博社'],
            ['很重要', '国内'],
            ['很重要', '研究报告'],
            ['很重要', '国外'],
            ['很重要', ''],
            ['重要', '国内'],
            ['重要', '路透社'],
            ['重要', '彭博社'],
        ]

        # 初始化新闻标题生成器
        try:
            self.news_summarizer = NewsSummarizer()
            self.ai_title_enabled = True
            logging.info("AI 标题生成功能已启用")
        except Exception as e:
            logging.warning(f"AI 标题生成功能初始化失败: {e}")
            self.news_summarizer = None
            self.ai_title_enabled = False
    
    def generate_news_id(self, news_item):
        """生成新闻的唯一ID（使用MD5）"""
        content = news_item.get('content', '')
        time_str = news_item.get('time', '')
        
        # 使用时间+内容生成MD5
        combined = f"{time_str}_{content}"
        md5_hash = hashlib.md5(combined.encode('utf-8')).hexdigest()
        return f"{time_str}_{md5_hash}"
    
    def load_sent_news(self):
        """加载已推送新闻记录"""
        try:
            with open(self.sent_news_file, "r", encoding="utf-8") as file:
                loaded_news = set(json.load(file))
                
                # 检查是否是旧格式（使用hash()函数的记录）
                # 新格式的MD5是32位十六进制字符串
                if loaded_news:
                    sample_id = next(iter(loaded_news))
                    if '_' in sample_id:
                        hash_part = sample_id.split('_', 1)[1]
                        # 如果不是32位十六进制，说明是旧格式
                        if not (len(hash_part) == 32 and all(c in '0123456789abcdef' for c in hash_part.lower())):
                            logging.warning("检测到旧格式的推送记录，清空并使用新的MD5格式")
                            print(f"{Fore.YELLOW}⚠️ 检测到旧格式记录，已自动清空，将使用新的MD5格式")
                            return set()
                
                logging.info(f"成功加载 {len(loaded_news)} 条已推送新闻记录")
                return loaded_news
        except FileNotFoundError:
            logging.info(f"推送记录文件 {self.sent_news_file} 不存在，创建新的记录")
            return set()
        except Exception as e:
            logging.error(f"加载已推送新闻记录失败: {e}")
            return set()
    
    def save_sent_news(self):
        """保存已推送新闻记录"""
        try:
            sent_news_list = list(self.sent_news)[-1000:]  # 只保留最新1000条记录
            with open(self.sent_news_file, "w", encoding="utf-8") as file:
                json.dump(sent_news_list, file, ensure_ascii=False, indent=2)
            logging.info(f"成功保存 {len(sent_news_list)} 条推送记录到 {self.sent_news_file}")
        except Exception as e:
            logging.error(f"保存已推送新闻记录失败: {e}")
    
    def get_page_content(self):
        """获取页面内容"""
        try:
            response = self.session.get(self.url, timeout=10)
            response.raise_for_status()
            return response.text
        except Exception as e:
            logging.error(f"获取页面失败: {e}")
            return None
    
    def extract_news_from_html(self, html_content):
        """从HTML中提取新闻数据"""
        try:
            soup = BeautifulSoup(html_content, 'html.parser')
            news_list = []
            
            # 从script标签中提取JSON数据
            news_list = self.extract_from_scripts(soup)
            
            return news_list
            
        except Exception as e:
            logging.error(f"解析HTML失败: {e}")
            return []
    
    def extract_from_scripts(self, soup):
        """从script标签中提取JSON数据"""
        news_list = []
        try:
            script_tags = soup.find_all('script')
            
            for script in script_tags:
                if not script.string:
                    continue
                    
                if 'justpyComponents' in script.string:
                    match = re.search(r'justpyComponents\s*=\s*(\[.*?\]);', script.string, re.DOTALL)
                    if match:
                        try:
                            json_str = match.group(1)
                            data = json.loads(json_str)
                            extracted_news = self.parse_justpy_data(data)
                            news_list.extend(extracted_news)
                            break
                        except Exception as e:
                            logging.error(f"解析justpyComponents失败: {e}")
                            continue
                            
        except Exception as e:
            logging.error(f"从script提取数据失败: {e}")
        
        return news_list
    
    def parse_justpy_data(self, data):
        """解析JustPy组件数据中的新闻"""
        news_list = []
        
        def find_news_cards(obj):
            if isinstance(obj, dict):
                attrs = obj.get('attrs', {})
                if attrs.get('name', '').startswith('card_'):
                    news_item = self.extract_news_from_card(obj)
                    if news_item:
                        news_list.append(news_item)
                
                for value in obj.values():
                    find_news_cards(value)
                    
            elif isinstance(obj, list):
                for item in obj:
                    find_news_cards(item)
        
        find_news_cards(data)
        return news_list
    
    def extract_news_from_card(self, card_obj):
        """从卡片对象中提取新闻信息"""
        try:
            news_item = {
                'time': '',
                'source': '',
                'importance': '',
                'content': ''
            }
            
            all_texts = []
            
            def collect_texts(obj):
                if isinstance(obj, dict):
                    if 'text' in obj and obj['text']:
                        text = obj['text'].strip()
                        if text:
                            all_texts.append(text)
                    
                    for value in obj.values():
                        collect_texts(value)
                        
                elif isinstance(obj, list):
                    for item in obj:
                        collect_texts(item)
            
            collect_texts(card_obj)
            combined_text = ' '.join(all_texts)
            
            # 提取时间
            time_pattern = r'(\d{4}-\d{2}-\d{2}\s+\d{2}:\d{2}:\d{2})'
            time_match = re.search(time_pattern, combined_text)
            if time_match:
                news_item['time'] = time_match.group(1)
            
            # 提取来源
            sources = ['国内', '彭博社', '路透社', '华尔街日报', '金融时报', '研究报告', 'RTRS', '新华社', '央视新闻']
            for source in sources:
                if source in combined_text:
                    news_item['source'] = source
                    break
            
            # 提取重要程度
            if '很重要' in combined_text:
                news_item['importance'] = '很重要'
            elif '重要' in combined_text:
                news_item['importance'] = '重要'
            
            # 提取主要内容
            content_candidates = []
            for text in all_texts:
                if (len(text) > 20 and 
                    not re.match(time_pattern, text) and
                    text not in ['很重要', '重要', '国内', '彭博社', '路透社', '华尔街日报', '金融时报', '研究报告', 'RTRS', '新华社', '央视新闻', 'AI解释'] and
                    not text.startswith('🤖')):
                    content_candidates.append(text)
            
            if content_candidates:
                news_item['content'] = max(content_candidates, key=len)
            
            if news_item['time'] and news_item['content']:
                return news_item
                
        except Exception as e:
            logging.error(f"从卡片提取新闻失败: {e}")
        
        return None
    
    def matches_push_filter(self, news_item):
        """检查新闻是否满足推送过滤条件"""
        if not self.push_filters:
            return True
        
        news_importance = news_item.get('importance', '')
        news_source = news_item.get('source', '')
        
        for filter_condition in self.push_filters:
            required_importance, required_source = filter_condition
            if news_importance == required_importance and news_source == required_source:
                return True
            
        
        return False
    
    def should_push_news(self, news_item):
        """判断是否应该推送新闻（不标记为已推送）"""
        if not self.matches_push_filter(news_item):
            return False
        
        news_id = self.generate_news_id(news_item)
        
        # 调试信息：检查新闻是否在已推送记录中
        is_sent = news_id in self.sent_news
        logging.debug(f"检查新闻 {news_id}: 已推送={is_sent}, 记录总数={len(self.sent_news)}")
        
        # 只检查是否已推送，不在这里标记
        return not is_sent
    
    def mark_news_as_sent(self, news_item):
        """标记新闻为已推送"""
        news_id = self.generate_news_id(news_item)
        self.sent_news.add(news_id)
        # 立即保存到文件
        self.save_sent_news()
        logging.info(f"标记新闻为已推送: {news_id}")
    
    def filter_unsent_news(self, news_list):
        """过滤掉已推送的新闻，返回未推送的新闻列表"""
        unsent_news = []
        for news in news_list:
            news_id = self.generate_news_id(news)
            if news_id not in self.sent_news:
                unsent_news.append(news)
        return unsent_news
    
    def is_new_news(self, news_item):
        """检查是否为新新闻"""
        news_id = self.generate_news_id(news_item)
        
        if news_id not in self.seen_news:
            self.seen_news.add(news_id)
            return True
        return False
    
    def push_batch_news_to_wechat(self, news_list):
        """批量推送多条新闻到微信"""
        if not self.enable_push or not news_list:
            return False
            
        try:
            total_count = len(news_list)
            sources = list(set([news.get('source', '未知') for news in news_list]))
            test__data = "测试数据123\n" +"测试数据321\n"+"测试数据312\n"
            # 使用 AI 生成标题
            if self.ai_title_enabled and self.news_summarizer:
                try:
                    ai_title = self.news_summarizer.generate_batch_title(news_list)
                    title = f"{ai_title} ({total_count}条)"+test__data
                    logging.info(f"AI 生成标题: {ai_title}")
                    print(f"{Fore.CYAN}🤖 AI 生成标题: {ai_title}")
                except Exception as e:
                    logging.warning(f"AI 标题生成失败，使用默认标题: {e}")
                    print(f"{Fore.YELLOW}⚠️ AI 标题生成失败，使用默认标题: {e}")
                    title = f"快讯汇总 ({total_count}条)"
                    if len(sources) <= 3:
                        title += f" - {'/'.join(sources)}"
            else:
                # 降级方案：使用原来的标题格式
                title = f"快讯汇总 ({total_count}条)"
                if len(sources) <= 3:
                    title += f" - {'/'.join(sources)}"
            
            message_parts = [
                title+"\n",
                f"<p><strong>⏰ 汇总时间:</strong> {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>",
                "<hr>"
            ]
            
            # 按过滤条件分组（重要程度+来源）
            grouped_news = {}
            for news in news_list:
                importance = news.get('importance', '其他')
                source = news.get('source', '未知')
                group_key = f"{importance}+{source}"
                
                if group_key not in grouped_news:
                    grouped_news[group_key] = []
                grouped_news[group_key].append(news)
            
            # 定义分组显示顺序和样式
            group_styles = {
                '很重要': {'emoji': '🔥', 'color': '#ff4444', 'priority': 1},
                '重要': {'emoji': '⚠️', 'color': '#ffaa00', 'priority': 2},
                '其他': {'emoji': '📰', 'color': '#0088cc', 'priority': 3}
            }
            
            # 按优先级排序分组
            sorted_groups = sorted(grouped_news.items(), 
                                 key=lambda x: (group_styles.get(x[0].split('+')[0], {'priority': 99})['priority'], x[0]))
            
            for group_key, group_news in sorted_groups:
                importance, source = group_key.split('+', 1)
                style = group_styles.get(importance, {'emoji': '📰', 'color': '#0088cc'})
                
                # 分组标题
                group_title = f"{style['emoji']} {importance} - {source} ({len(group_news)}条)"
                message_parts.append(f"<h3>{group_title}</h3>")
                
                # 按时间排序该分组的新闻
                group_news.sort(key=lambda x: x.get('time', ''), reverse=True)
                
                for i, news in enumerate(group_news, 1):
                    message_parts.append(f"""<div style="margin-bottom: 8px; padding: 8px; border-left: 3px solid {style['color']};">
<p style="margin: 0 0 4px 0;"><strong>{i}. {news.get('time', '')}</strong></p>
<p style="margin: 0;">{news.get('content', '')}</p>
</div>""")
            
            message = "".join(message_parts)
            
            response = requests.post(
                self.wechat_api_url,
                data={
                    "token": self.wechat_token,
                    "title": title,
                    "content": message,
                    "topic": "newsgroup" #个人
                    # "topic": "xgroup" #群组
                },
                timeout=15
            )
            
            if response.status_code == 200:
                # 推送成功后，标记所有新闻为已推送
                for news in news_list:
                    self.mark_news_as_sent(news)
                
                logging.info(f"批量推送成功: {total_count}条新闻")
                print(f"{Fore.GREEN}📱 批量推送成功 ({total_count}条)")
                return True
            else:
                logging.error(f"批量推送失败，状态码: {response.status_code}")
                print(f"{Fore.RED}📱 批量推送失败: HTTP {response.status_code}")
                return False
                
        except Exception as e:
            logging.error(f"批量推送失败: {e}")
            print(f"{Fore.RED}📱 批量推送失败: {e}")
            return False
    
    def format_news_display(self, news_item):
        """格式化新闻显示"""
        if news_item['importance'] == '很重要':
            importance_color = Back.RED + Fore.WHITE
            content_color = Fore.RED + Style.BRIGHT
        elif news_item['importance'] == '重要':
            importance_color = Back.YELLOW + Fore.BLACK
            content_color = Fore.YELLOW + Style.BRIGHT
        else:
            importance_color = Back.BLUE + Fore.WHITE
            content_color = Fore.CYAN
        
        output_lines = []
        output_lines.append("=" * 80)
        output_lines.append(f"{Fore.GREEN}⏰ 时间: {news_item['time']}")
        
        if news_item['source']:
            output_lines.append(f"{Fore.BLUE}📰 来源: {news_item['source']}")
        
        if news_item['importance']:
            output_lines.append(f"🔥 重要性: {importance_color} {news_item['importance']} {Style.RESET_ALL}")
        
        output_lines.append(f"{content_color}📄 内容:")
        output_lines.append(f"{content_color}{news_item['content']}")
        output_lines.append("=" * 80)
        output_lines.append("")
        
        return "\n".join(output_lines)
    
    def monitor(self, push_interval=900):
        """开始监控
        Args:
            push_interval: 推送间隔（秒），默认15分钟
        """
        print(f"{Fore.GREEN}� 开始监控 站Jasper 快讯...")
        print(f"{Fore.CYAN}⏱️  获取新闻间隔: {self.fetch_interval}秒")
        print(f"{Fore.CYAN}📦 批量推送间隔: {push_interval}秒 ({push_interval//60}分钟)")
        print(f"{Fore.CYAN}🛑 按 Ctrl+C 停止监控")
        print(f"{Fore.MAGENTA}📊 监控网站: {self.url}")
        
        if self.enable_push:
            print(f"{Fore.MAGENTA}📦 批量推送模式")
            print(f"{Fore.CYAN}📝 新闻将每{self.fetch_interval}秒获取，每{push_interval//60}分钟批量推送一次")
            
            if self.push_filters:
                print(f"{Fore.YELLOW}🔍 推送过滤条件:")
                for i, (importance, source) in enumerate(self.push_filters, 1):
                    print(f"{Fore.YELLOW}   {i}. {importance} + {source}")
        
        print("=" * 80)
        
        while True:
            try:
                current_time = datetime.now().strftime('%H:%M:%S')
                print(f"{Fore.CYAN}[{current_time}] 正在检查新快讯...")
                
                html_content = self.get_page_content()
                
                if html_content:
                    news_list = self.extract_news_from_html(html_content)
                    
                    if news_list:
                        new_count = 0
                        push_count = 0
                        
                        for news in news_list:
                            if self.is_new_news(news):
                                print(self.format_news_display(news))
                                new_count += 1
                                
                                # 检查是否需要推送（添加到待推送队列）
                                if self.should_push_news(news):
                                    self.pending_push_news.append(news)
                                    push_count += 1
                                    news_id = self.generate_news_id(news)
                                    print(f"{Fore.CYAN}📝 添加到批量推送队列: {news_id}")
                                elif self.enable_push and not self.matches_push_filter(news):
                                    print(f"{Fore.YELLOW}🔍 过滤: 不满足推送条件 [{news.get('importance', '无')}+{news.get('source', '无')}]")
                                elif self.enable_push:
                                    news_id = self.generate_news_id(news)
                                    print(f"{Fore.YELLOW}🔍 过滤: 已推送过 {news_id}")
                        
                        if new_count > 0:
                            print(f"{Fore.GREEN}✅ 发现 {new_count} 条新快讯")
                            if self.enable_push and push_count > 0:
                                print(f"{Fore.GREEN}📝 已添加 {push_count} 条到批量推送队列 (队列总数: {len(self.pending_push_news)})")
                        else:
                            print(f"{Fore.CYAN}ℹ️  暂无新快讯")
                    else:
                        print(f"{Fore.YELLOW}⚠️  未解析到新闻数据")
                else:
                    print(f"{Fore.RED}❌ 获取页面内容失败")
                
                # 检查是否到了推送时间
                current_timestamp = time.time()
                if self.enable_push and self.pending_push_news and (current_timestamp - self.last_push_time) >= push_interval:
                    # 过滤掉已推送的新闻
                    unsent_news = self.filter_unsent_news(self.pending_push_news)
                    if unsent_news:
                        print(f"{Fore.MAGENTA}📦 到达推送时间，准备批量推送 {len(unsent_news)} 条新闻...")
                        if len(unsent_news) < len(self.pending_push_news):
                            filtered_count = len(self.pending_push_news) - len(unsent_news)
                            print(f"{Fore.YELLOW}🔍 过滤掉 {filtered_count} 条已推送的重复新闻")
                        
                        if self.push_batch_news_to_wechat(unsent_news):
                            self.pending_push_news.clear()
                            self.last_push_time = current_timestamp
                        else:
                            print(f"{Fore.YELLOW}⚠️ 批量推送失败，将在下次尝试")
                    else:
                        print(f"{Fore.CYAN}📦 待推送队列中的新闻都已推送过，清空队列")
                        self.pending_push_news.clear()
                        self.last_push_time = current_timestamp
                
                # 移除定期保存，改为实时保存
                
                time.sleep(self.fetch_interval)
                
            except KeyboardInterrupt:
                print(f"\n{Fore.GREEN}🛑 监控已停止")
                
                # 处理剩余的待推送新闻
                if self.enable_push and self.pending_push_news:
                    # 过滤掉已推送的新闻
                    unsent_news = self.filter_unsent_news(self.pending_push_news)
                    if unsent_news:
                        print(f"{Fore.MAGENTA}📦 推送剩余的 {len(unsent_news)} 条新闻...")
                        self.push_batch_news_to_wechat(unsent_news)
                    else:
                        print(f"{Fore.CYAN}📦 剩余新闻都已推送过")
                    self.pending_push_news.clear()
                
                if self.enable_push:
                    self.save_sent_news()
                    print(f"{Fore.CYAN}💾 已保存推送记录")
                break
            except Exception as e:
                logging.error(f"监控出错: {e}")
                print(f"{Fore.RED}❌ 监控出错: {e}")
                time.sleep(self.fetch_interval)

def main():
    """主函数"""
    push_interval = 10 * 1  # 默认15分钟推送一次
    enable_push = True
    
    if len(sys.argv) > 1:
        if sys.argv[1] == 'no-push':
            enable_push = False
            print(f"{Fore.YELLOW}📱 推送功能已禁用")
        else:
            try:
                push_interval = int(sys.argv[1])
                if push_interval < 60:
                    print(f"{Fore.YELLOW}⚠️  推送间隔不能小于60秒，使用默认值15分钟")
                    push_interval = 60 * 15
            except ValueError:
                print(f"{Fore.YELLOW}⚠️  无效的推送间隔时间，使用默认值15分钟")
    
    if len(sys.argv) > 2 and sys.argv[2] == 'no-push':
        enable_push = False
        print(f"{Fore.YELLOW}📱 推送功能已禁用")
    
    monitor = JasperBatchMonitor(enable_push=enable_push)
    
    if enable_push:
        print(f"{Fore.GREEN}📱 推送功能已启用")
        print(f"{Fore.CYAN}📝 已加载 {len(monitor.sent_news)} 条推送记录")

        # 显示 AI 标题生成状态
        if monitor.ai_title_enabled:
            print(f"{Fore.GREEN}🤖 AI 标题生成功能已启用 (模型: {monitor.news_summarizer.model})")
        else:
            print(f"{Fore.YELLOW}⚠️ AI 标题生成功能未启用，将使用默认标题格式")
        
        # 调试信息：显示最近几条已推送记录
        if monitor.sent_news:
            recent_news = list(monitor.sent_news)[-5:]  # 显示最近5条
            print(f"{Fore.CYAN}🔍 最近推送记录示例:")
            for news_id in recent_news:
                print(f"{Fore.CYAN}   - {news_id}")
        
        # 临时启用调试日志
        logging.getLogger().setLevel(logging.DEBUG)
        
        # 提示用户：由于改用MD5，建议清空旧记录
        print(f"{Fore.YELLOW}💡 提示: 如果仍有重复推送，请删除 {monitor.sent_news_file} 文件重新开始")
    
    monitor.monitor(push_interval)

if __name__ == "__main__":
    main()