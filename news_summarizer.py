import requests
import json
from typing import List, Dict, Optional
import logging
import subprocess
import time
import os

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class NewsSummarizer:
    def __init__(self, ollama_url: str = "http://127.0.0.1:11434/api/generate", model: str = "qwen3:0.6b"):
        self.ollama_url = ollama_url
        self.model = model
        self.ollama_base_url = ollama_url.replace('/api/generate', '')

        # 检查并启动 Ollama 服务
        self._ensure_ollama_service()

    def _check_ollama_service(self) -> bool:
        """检查 Ollama 服务是否运行"""
        try:
            response = requests.get(f"{self.ollama_base_url}/api/tags", timeout=5)
            return response.status_code == 200
        except Exception as e:
            logger.debug(f"检查 Ollama 服务失败: {e}")
            return False

    def _start_ollama_service(self) -> bool:
        """启动 Ollama 服务"""
        try:
            logger.info("正在启动 Ollama 服务...")

            # 尝试启动 Ollama 服务
            # 使用 subprocess.Popen 在后台启动
            process = subprocess.Popen(
                ["ollama", "serve"],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                start_new_session=True
            )

            # 等待服务启动
            max_wait = 30  # 最多等待30秒
            for i in range(max_wait):
                time.sleep(1)
                if self._check_ollama_service():
                    logger.info("Ollama 服务启动成功")
                    return True
                if i % 5 == 0:
                    logger.info(f"等待 Ollama 服务启动... ({i+1}/{max_wait})")

            logger.error("Ollama 服务启动超时")
            return False

        except FileNotFoundError:
            logger.error("未找到 ollama 命令，请确保 Ollama 已正确安装")
            return False
        except Exception as e:
            logger.error(f"启动 Ollama 服务失败: {e}")
            return False

    def _check_model_available(self) -> bool:
        """检查指定模型是否可用"""
        try:
            response = requests.get(f"{self.ollama_base_url}/api/tags", timeout=10)
            if response.status_code == 200:
                models = response.json().get('models', [])
                model_names = [model.get('name', '') for model in models]

                # 检查完整模型名或部分匹配
                for model_name in model_names:
                    if self.model in model_name or model_name.startswith(self.model.split(':')[0]):
                        logger.info(f"找到可用模型: {model_name}")
                        return True

                logger.warning(f"模型 {self.model} 不可用，可用模型: {model_names}")
                return False
            return False
        except Exception as e:
            logger.error(f"检查模型可用性失败: {e}")
            return False

    def _ensure_ollama_service(self):
        """确保 Ollama 服务运行并且模型可用"""
        # 检查服务是否运行
        if not self._check_ollama_service():
            logger.info("Ollama 服务未运行，尝试启动...")
            if not self._start_ollama_service():
                logger.error("无法启动 Ollama 服务")
                return

        # 检查模型是否可用
        if not self._check_model_available():
            logger.warning(f"模型 {self.model} 不可用，AI 标题生成功能可能无法正常工作")

    def _call_ollama(self, prompt: str) -> Optional[str]:
        """调用Ollama API"""
        payload = {
            "model": self.model,
            "prompt": prompt,
            "stream": False
        }
        
        try:
            resp = requests.post(self.ollama_url, json=payload, timeout=30)
            if resp.status_code == 200:
                result = resp.json()
                return result.get('response', '').strip()
            else:
                logger.error(f"Ollama API error: HTTP {resp.status_code}")
                return None
        except Exception as e:
            logger.error(f"调用Ollama API失败: {e}")
            return None
    
    def _clean_response(self, response: str) -> str:
        """清理AI响应，移除思考过程等无关内容"""
        if not response:
            return response
        
        # 移除 <think> 标签及其内容
        import re
        cleaned = re.sub(r'<think>.*?</think>', '', response, flags=re.DOTALL)
        
        # 移除多余的空白字符
        cleaned = re.sub(r'\s+', ' ', cleaned).strip()
        
        # 如果清理后为空，返回原响应的第一行
        if not cleaned:
            lines = response.split('\n')
            for line in lines:
                line = line.strip()
                if line and not line.startswith('<') and not line.endswith('>'):
                    return line
        
        return cleaned
    
    def _create_summary_prompt(self, news_items: List[Dict]) -> str:
        """创建新闻总结的prompt"""
        if len(news_items) == 1:
            # 单条新闻
            news = news_items[0]
            content = news.get('content', '')
            source = news.get('source', '')
            
            prompt = f"""请根据以下新闻内容生成一个具体直观的标题，要求：
1. 标题长度控制在15-30个字符
2. 必须包含具体的主体（公司名、机构名、人名等）
3. 必须包含具体的事件或行动
4. 如果有具体数字、百分比、金额等，优先包含
5. 避免使用"宣布"、"表示"等转述词汇，直接说事件本身
6. 让读者一看标题就知道发生了什么具体事情

新闻内容：{content}
{f'来源：{source}' if source else ''}

请生成一个具体、直观的标题，突出最关键的事实信息："""
        else:
            # 多条新闻
            news_content = ""
            for i, news in enumerate(news_items, 1):
                content = news.get('content', '')
                source = news.get('source', '')
                news_content += f"""
新闻{i}：
内容：{content}
{f'来源：{source}' if source else ''}
"""
            
            prompt = f"""请为以下{len(news_items)}条新闻分别生成具体直观的标题，要求：
1. 每个标题长度控制在15-30个字符
2. 必须包含具体的主体和事件
3. 如果有数字、百分比等具体数据，优先包含
4. 避免使用宽泛词汇，要具体明确
5. 按顺序返回，格式为：1. 标题一 2. 标题二 ...
6. 让读者一看就知道每条新闻的具体内容

新闻内容：{news_content}

请按要求格式返回具体、直观的标题："""
        
        return prompt
    
    def summarize_news(self, news_items: List[Dict]) -> List[str]:
        """
        根据新闻内容生成标题
        
        Args:
            news_items: 新闻列表，每个元素包含 content 字段（必需），source 字段（可选）
            
        Returns:
            生成的标题列表
        """
        if not news_items:
            return []
        
        prompt = self._create_summary_prompt(news_items)
        response = self._call_ollama(prompt)
        
        if not response:
            logger.warning("AI生成标题失败，返回默认标题")
            return [f"新闻{i+1}" for i in range(len(news_items))]
        
        # 解析响应
        if len(news_items) == 1:
            # 清理响应，移除可能的思考过程标签
            cleaned_response = self._clean_response(response)
            return [cleaned_response]
        else:
            # 解析多条新闻的响应
            titles = []
            lines = response.split('\n')
            for line in lines:
                line = line.strip()
                if line and (line.startswith(tuple(f"{i}." for i in range(1, 20))) or 
                           line.startswith(tuple(f"{i}、" for i in range(1, 20)))):
                    # 提取标题内容
                    title = line.split('.', 1)[-1].split('、', 1)[-1].strip()
                    if title:
                        # 清理标题
                        title = self._clean_response(title)
                        titles.append(title)
            
            # 如果解析失败，生成默认标题
            if len(titles) != len(news_items):
                logger.warning("解析AI响应失败，返回默认标题")
                return [f"新闻{i+1}" for i in range(len(news_items))]
            
            return titles

    def generate_batch_title(self, news_items: List[Dict]) -> str:
        """
        为一批新闻生成总体标题

        Args:
            news_items: 新闻列表，每个元素包含 content 字段（必需），source 字段（可选）

        Returns:
            生成的总体标题
        """
        if not news_items:
            return "快讯汇总"

        # 创建批量标题生成的 prompt
        prompt = self._create_batch_title_prompt(news_items)
        response = self._call_ollama(prompt)

        if not response:
            logger.warning("AI生成批量标题失败，返回默认标题")
            # 降级方案：基于来源和数量生成标题
            return self._generate_fallback_title(news_items)

        # 清理响应
        cleaned_title = self._clean_response(response)

        return cleaned_title

    def _create_batch_title_prompt(self, news_items: List[Dict]) -> str:
        """创建批量新闻标题生成的prompt"""
        total_count = len(news_items)

        # 提取新闻关键信息
        news_summaries = []
        sources = set()
        key_entities = []  # 关键实体
        key_events = []    # 关键事件

        for i, news in enumerate(news_items[:5], 1):  # 最多处理前5条新闻
            content = news.get('content', '')[:150]  # 增加内容长度
            source = news.get('source', '')
            if source:
                sources.add(source)

            # 提取关键词和实体
            import re
            # 提取公司名、机构名、国家名等
            entities = re.findall(r'[A-Z][a-z]*(?:\s+[A-Z][a-z]*)*|[\u4e00-\u9fff]+(?:公司|银行|央行|政府|委员会|部|局)', content)
            # 提取关键动作词
            events = re.findall(r'(宣布|发布|启动|上涨|下跌|增长|下降|维持|调整|推出|收购|合并|投资)', content)

            key_entities.extend(entities[:2])  # 每条新闻最多取2个实体
            key_events.extend(events[:2])     # 每条新闻最多取2个事件

            news_summaries.append(f"{i}. {content}")

        # 去重并限制数量
        key_entities = list(set(key_entities))[:3]
        key_events = list(set(key_events))[:3]

        sources_text = "、".join(list(sources)[:3]) if sources else "多个来源"
        entities_text = "、".join(key_entities) if key_entities else ""
        events_text = "、".join(key_events) if key_events else ""

        prompt = f"""请为以下{total_count}条新闻生成一个具体直观的汇总标题。

重要：这是{total_count}条不同的新闻，必须用分号(;)分隔每条新闻的核心内容！

格式要求：
- 如果是2条新闻：新闻1核心内容;新闻2核心内容
- 如果是3条新闻：新闻1核心内容;新闻2核心内容;新闻3核心内容
- 每条新闻的核心内容要包含：主体+关键数据+事件

内容要求：
1. 每条新闻内容控制在8-15个字符
2. 必须包含具体主体（公司名、机构名等）
3. 必须包含关键数据（数字、百分比等）
4. 避免"宣布"、"发布"等冗余词汇
5. 语言简洁有力

关键实体：{entities_text}
关键事件：{events_text}

新闻内容：
{chr(10).join(news_summaries)}

请严格按照"内容1;内容2;内容3"的格式生成标题："""

        return prompt

    def _generate_fallback_title(self, news_items: List[Dict]) -> str:
        """生成降级标题"""
        total_count = len(news_items)
        sources = list(set([news.get('source', '未知') for news in news_items if news.get('source')]))

        if len(sources) == 1:
            return f"{sources[0]}要闻 ({total_count}条)"
        elif len(sources) <= 3:
            return f"{'/'.join(sources)}要闻 ({total_count}条)"
        else:
            return f"多源要闻汇总 ({total_count}条)"

def create_news_summary_api():
    """创建新闻总结API接口"""
    summarizer = NewsSummarizer()
    
    def summarize_news_api(news_data: List[Dict]) -> Dict:
        """
        新闻总结API接口
        
        Args:
            news_data: 新闻数据列表，格式：
            [
                {
                    "content": "新闻内容",
                    "source": "新闻来源（可选）"
                }
            ]
            
        Returns:
            {
                "success": True/False,
                "data": ["优化标题1", "优化标题2", ...],
                "message": "处理信息"
            }
        """
        try:
            if not news_data or not isinstance(news_data, list):
                return {
                    "success": False,
                    "data": [],
                    "message": "无效的新闻数据"
                }
            
            # 验证数据格式
            for news in news_data:
                if not isinstance(news, dict) or 'content' not in news:
                    return {
                        "success": False,
                        "data": [],
                        "message": "新闻数据格式错误，需要包含content字段"
                    }
            
            # 调用总结服务
            summarized_titles = summarizer.summarize_news(news_data)
            
            return {
                "success": True,
                "data": summarized_titles,
                "message": f"成功处理{len(summarized_titles)}条新闻"
            }
            
        except Exception as e:
            logger.error(f"新闻总结API错误: {e}")
            return {
                "success": False,
                "data": [],
                "message": f"处理失败: {str(e)}"
            }
    
    return summarize_news_api

# 使用示例
if __name__ == "__main__":
    # 创建API接口
    api = create_news_summary_api()
    
    # 测试数据
    test_news = [
        {
            "title": "某公司发布新产品，预计将改变行业格局",
            "content": "某知名科技公司今日正式发布了其最新研发的人工智能产品，该产品具有革命性的技术突破，预计将对整个行业产生深远影响。公司CEO表示，这款产品经过三年的研发，集成了最先进的AI技术。",
            "source": "科技日报"
        },
        {
            "title": "股市今日大幅波动，投资者需谨慎",
            "content": "今日股市开盘后出现大幅波动，主要指数均有不同程度的下跌。分析师认为，这主要受到国际市场不确定性因素的影响。建议投资者保持理性，谨慎操作。",
            "source": "财经网"
        }
    ]
    
    # 调用API
    result = api(test_news)
    print("API响应:")
    print(json.dumps(result, ensure_ascii=False, indent=2))