#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Ollama 服务启动和管理脚本
"""

import subprocess
import time
import requests
import sys
import os
from colorama import init, Fore, Style

# 初始化colorama
init(autoreset=True)

class OllamaManager:
    def __init__(self, port=11434):
        self.port = port
        self.base_url = f"http://127.0.0.1:{port}"
        self.api_url = f"{self.base_url}/api"
        
    def check_service(self):
        """检查 Ollama 服务是否运行"""
        try:
            response = requests.get(f"{self.api_url}/tags", timeout=5)
            return response.status_code == 200
        except:
            return False
    
    def start_service(self):
        """启动 Ollama 服务"""
        print(f"{Fore.CYAN}🚀 启动 Ollama 服务 (端口: {self.port})...")
        
        try:
            # 设置环境变量指定端口
            env = os.environ.copy()
            env['OLLAMA_HOST'] = f"127.0.0.1:{self.port}"
            
            # 启动服务
            process = subprocess.Popen(
                ["ollama", "serve"],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                env=env,
                start_new_session=True
            )
            
            print(f"{Fore.YELLOW}⏳ 等待服务启动...")
            
            # 等待服务启动
            max_wait = 30
            for i in range(max_wait):
                time.sleep(1)
                if self.check_service():
                    print(f"{Fore.GREEN}✅ Ollama 服务启动成功!")
                    return True
                
                if i % 5 == 0 and i > 0:
                    print(f"{Fore.YELLOW}⏳ 等待中... ({i}/{max_wait})")
            
            print(f"{Fore.RED}❌ 服务启动超时")
            return False
            
        except FileNotFoundError:
            print(f"{Fore.RED}❌ 未找到 ollama 命令")
            print(f"{Fore.YELLOW}💡 请确保 Ollama 已正确安装")
            print(f"{Fore.YELLOW}   安装方法: https://ollama.ai/")
            return False
        except Exception as e:
            print(f"{Fore.RED}❌ 启动失败: {e}")
            return False
    
    def list_models(self):
        """列出可用模型"""
        try:
            response = requests.get(f"{self.api_url}/tags", timeout=10)
            if response.status_code == 200:
                models = response.json().get('models', [])
                if models:
                    print(f"{Fore.GREEN}📋 可用模型:")
                    for model in models:
                        name = model.get('name', 'Unknown')
                        size = model.get('size', 0)
                        size_mb = size / (1024 * 1024) if size > 0 else 0
                        print(f"  • {name} ({size_mb:.1f} MB)")
                else:
                    print(f"{Fore.YELLOW}📋 暂无可用模型")
                return models
            else:
                print(f"{Fore.RED}❌ 获取模型列表失败: HTTP {response.status_code}")
                return []
        except Exception as e:
            print(f"{Fore.RED}❌ 获取模型列表失败: {e}")
            return []
    
    def pull_model(self, model_name):
        """下载模型"""
        print(f"{Fore.CYAN}📥 下载模型: {model_name}")
        
        try:
            # 使用 ollama pull 命令下载模型
            process = subprocess.run(
                ["ollama", "pull", model_name],
                capture_output=True,
                text=True,
                timeout=600  # 10分钟超时
            )
            
            if process.returncode == 0:
                print(f"{Fore.GREEN}✅ 模型 {model_name} 下载成功")
                return True
            else:
                print(f"{Fore.RED}❌ 模型下载失败:")
                print(f"{Fore.RED}   {process.stderr}")
                return False
                
        except subprocess.TimeoutExpired:
            print(f"{Fore.RED}❌ 模型下载超时")
            return False
        except Exception as e:
            print(f"{Fore.RED}❌ 模型下载失败: {e}")
            return False
    
    def test_model(self, model_name):
        """测试模型"""
        print(f"{Fore.CYAN}🧪 测试模型: {model_name}")
        
        try:
            payload = {
                "model": model_name,
                "prompt": "你好，请简单介绍一下自己",
                "stream": False
            }
            
            response = requests.post(
                f"{self.api_url}/generate",
                json=payload,
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                generated_text = result.get('response', '').strip()
                print(f"{Fore.GREEN}✅ 模型测试成功")
                print(f"{Fore.CYAN}📝 生成内容: {generated_text[:100]}...")
                return True
            else:
                print(f"{Fore.RED}❌ 模型测试失败: HTTP {response.status_code}")
                return False
                
        except Exception as e:
            print(f"{Fore.RED}❌ 模型测试失败: {e}")
            return False

def main():
    """主函数"""
    print(f"{Fore.GREEN}🤖 Ollama 服务管理器")
    print("=" * 50)
    
    manager = OllamaManager()
    
    # 检查服务状态
    print(f"{Fore.CYAN}🔍 检查服务状态...")
    if manager.check_service():
        print(f"{Fore.GREEN}✅ Ollama 服务已运行")
    else:
        print(f"{Fore.YELLOW}⚠️ Ollama 服务未运行")
        
        # 尝试启动服务
        if not manager.start_service():
            print(f"{Fore.RED}❌ 无法启动服务，退出")
            sys.exit(1)
    
    # 列出可用模型
    print(f"\n{Fore.CYAN}📋 检查可用模型...")
    models = manager.list_models()
    
    # 检查是否有 qwen3:0.6b 模型
    target_model = "qwen3:0.6b"
    model_names = [model.get('name', '') for model in models]
    
    if not any(target_model in name for name in model_names):
        print(f"\n{Fore.YELLOW}⚠️ 未找到目标模型 {target_model}")
        
        # 询问是否下载
        try:
            choice = input(f"{Fore.CYAN}是否下载模型 {target_model}? (y/n): ").lower().strip()
            if choice in ['y', 'yes', '是']:
                if manager.pull_model(target_model):
                    print(f"{Fore.GREEN}✅ 模型下载完成")
                else:
                    print(f"{Fore.RED}❌ 模型下载失败")
                    sys.exit(1)
            else:
                print(f"{Fore.YELLOW}⚠️ 跳过模型下载")
        except KeyboardInterrupt:
            print(f"\n{Fore.YELLOW}⚠️ 用户取消操作")
            sys.exit(0)
    else:
        print(f"{Fore.GREEN}✅ 找到目标模型 {target_model}")
    
    # 测试模型
    print(f"\n{Fore.CYAN}🧪 测试模型功能...")
    if manager.test_model(target_model):
        print(f"\n{Fore.GREEN}🎉 Ollama 服务配置完成!")
        print(f"{Fore.CYAN}💡 现在可以运行新闻推送脚本了")
    else:
        print(f"\n{Fore.RED}❌ 模型测试失败")
        sys.exit(1)

if __name__ == "__main__":
    main()
