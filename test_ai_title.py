#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试 AI 标题生成功能
"""

from news_summarizer import NewsSummarizer
import json

def test_ai_title_generation():
    """测试 AI 标题生成功能"""
    print("🤖 测试 AI 标题生成功能...")
    
    # 初始化新闻总结器
    try:
        summarizer = NewsSummarizer()
        print("✅ NewsSummarizer 初始化成功")
    except Exception as e:
        print(f"❌ NewsSummarizer 初始化失败: {e}")
        return
    
    # 测试数据
    test_news = [
        {
            "content": "美联储宣布维持基准利率不变，符合市场预期。美联储主席鲍威尔表示，当前经济数据支持维持现有货币政策立场。",
            "source": "路透社",
            "time": "2024-01-15 14:30:00",
            "importance": "很重要"
        },
        {
            "content": "中国央行今日进行1000亿元逆回购操作，利率维持在2.0%不变。这是央行连续第三个交易日进行逆回购操作。",
            "source": "国内",
            "time": "2024-01-15 15:00:00", 
            "importance": "很重要"
        },
        {
            "content": "苹果公司发布最新财报，第四季度营收创历史新高。iPhone销量超出分析师预期，推动股价盘后上涨3%。",
            "source": "彭博社",
            "time": "2024-01-15 16:00:00",
            "importance": "很重要"
        }
    ]
    
    print(f"\n📰 测试新闻数据 ({len(test_news)}条):")
    for i, news in enumerate(test_news, 1):
        print(f"  {i}. [{news['source']}] {news['content'][:50]}...")
    
    # 测试批量标题生成
    print("\n🎯 测试批量标题生成...")
    try:
        batch_title = summarizer.generate_batch_title(test_news)
        print(f"✅ 生成的批量标题: {batch_title}")
    except Exception as e:
        print(f"❌ 批量标题生成失败: {e}")
    
    # 测试单条新闻标题生成
    print("\n🎯 测试单条新闻标题生成...")
    try:
        single_titles = summarizer.summarize_news([test_news[0]])
        print(f"✅ 生成的单条标题: {single_titles[0]}")
    except Exception as e:
        print(f"❌ 单条标题生成失败: {e}")
    
    # 测试多条新闻标题生成
    print("\n🎯 测试多条新闻标题生成...")
    try:
        multiple_titles = summarizer.summarize_news(test_news)
        print(f"✅ 生成的多条标题:")
        for i, title in enumerate(multiple_titles, 1):
            print(f"  {i}. {title}")
    except Exception as e:
        print(f"❌ 多条标题生成失败: {e}")

def test_service_management():
    """测试服务管理功能"""
    print("\n🔧 测试服务管理功能...")
    
    summarizer = NewsSummarizer()
    
    # 测试服务检查
    print("🔍 检查 Ollama 服务状态...")
    if summarizer._check_ollama_service():
        print("✅ Ollama 服务正在运行")
    else:
        print("❌ Ollama 服务未运行")
    
    # 测试模型可用性
    print("🔍 检查模型可用性...")
    if summarizer._check_model_available():
        print(f"✅ 模型 {summarizer.model} 可用")
    else:
        print(f"❌ 模型 {summarizer.model} 不可用")

if __name__ == "__main__":
    print("=" * 60)
    print("🧪 AI 标题生成功能测试")
    print("=" * 60)
    
    # 测试服务管理
    test_service_management()
    
    # 测试标题生成
    test_ai_title_generation()
    
    print("\n" + "=" * 60)
    print("✅ 测试完成")
