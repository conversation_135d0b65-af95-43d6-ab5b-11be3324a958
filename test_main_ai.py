#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试主脚本中的 AI 标题生成功能
"""

from jasper_batch_push_test import JasperBatchMonitor
from colorama import init, Fore

# 初始化colorama
init(autoreset=True)

def test_main_ai_integration():
    """测试主脚本中的 AI 集成"""
    print(f"{Fore.GREEN}🧪 测试主脚本 AI 集成功能")
    print("=" * 60)
    
    # 创建监控器实例（禁用推送功能）
    monitor = JasperBatchMonitor(enable_push=False)
    
    # 检查 AI 功能状态
    print(f"{Fore.CYAN}🔍 检查 AI 功能状态...")
    if monitor.ai_title_enabled:
        print(f"{Fore.GREEN}✅ AI 标题生成功能已启用")
        print(f"{Fore.CYAN}📝 使用模型: {monitor.news_summarizer.model}")
    else:
        print(f"{Fore.RED}❌ AI 标题生成功能未启用")
        return
    
    # 创建测试新闻数据
    test_news = [
        {
            'time': '2024-01-15 14:30:00',
            'source': '路透社',
            'importance': '很重要',
            'content': '美联储宣布维持基准利率不变，符合市场预期。美联储主席鲍威尔表示，当前经济数据支持维持现有货币政策立场。'
        },
        {
            'time': '2024-01-15 15:00:00',
            'source': '国内',
            'importance': '很重要',
            'content': '中国央行今日进行1000亿元逆回购操作，利率维持在2.0%不变。这是央行连续第三个交易日进行逆回购操作。'
        },
        {
            'time': '2024-01-15 16:00:00',
            'source': '彭博社',
            'importance': '很重要',
            'content': '苹果公司发布最新财报，第四季度营收创历史新高。iPhone销量超出分析师预期，推动股价盘后上涨3%。'
        }
    ]
    
    print(f"\n{Fore.CYAN}📰 测试新闻数据 ({len(test_news)}条):")
    for i, news in enumerate(test_news, 1):
        print(f"  {i}. [{news['source']}] {news['content'][:50]}...")
    
    # 测试 AI 标题生成
    print(f"\n{Fore.CYAN}🤖 测试 AI 标题生成...")
    try:
        ai_title = monitor.news_summarizer.generate_batch_title(test_news)
        final_title = f"{ai_title} ({len(test_news)}条)"
        print(f"{Fore.GREEN}✅ AI 生成标题: {ai_title}")
        print(f"{Fore.GREEN}✅ 最终推送标题: {final_title}")
    except Exception as e:
        print(f"{Fore.RED}❌ AI 标题生成失败: {e}")
    
    # 测试降级机制
    print(f"\n{Fore.CYAN}🔄 测试降级机制...")
    
    # 临时禁用 AI 功能
    original_ai_enabled = monitor.ai_title_enabled
    monitor.ai_title_enabled = False
    
    # 生成降级标题
    sources = list(set([news.get('source', '未知') for news in test_news]))
    fallback_title = f"快讯汇总 ({len(test_news)}条)"
    if len(sources) <= 3:
        fallback_title += f" - {'/'.join(sources)}"
    
    print(f"{Fore.YELLOW}⚠️ 降级标题: {fallback_title}")
    
    # 恢复 AI 功能
    monitor.ai_title_enabled = original_ai_enabled
    
    print(f"\n{Fore.GREEN}🎉 测试完成！")

if __name__ == "__main__":
    test_main_ai_integration()
