#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试具体化标题生成效果
"""

from news_summarizer import NewsSummarizer
from colorama import init, Fore, Style

# 初始化colorama
init(autoreset=True)

def test_specific_scenarios():
    """测试各种具体场景的标题生成"""
    print(f"{Fore.GREEN}🎯 测试具体化标题生成效果")
    print("=" * 60)
    
    # 初始化
    summarizer = NewsSummarizer()
    
    # 测试场景
    scenarios = [
        {
            "name": "央行政策新闻",
            "news": [
                {
                    "content": "中国人民银行今日宣布下调存款准备金率0.5个百分点，释放流动性约1万亿元。这是央行今年第二次降准操作。",
                    "source": "国内"
                },
                {
                    "content": "美联储宣布加息25个基点，将联邦基金利率上调至5.25%-5.5%区间。这是美联储今年第11次加息。",
                    "source": "路透社"
                }
            ]
        },
        {
            "name": "科技公司财报",
            "news": [
                {
                    "content": "腾讯控股发布第三季度财报，营收1546亿元，同比增长10%。游戏业务收入达到459亿元，环比增长4%。",
                    "source": "彭博社"
                },
                {
                    "content": "阿里巴巴集团公布季度业绩，云计算业务收入276亿元，同比增长2%。淘宝天猫商业收入为703亿元。",
                    "source": "华尔街日报"
                }
            ]
        },
        {
            "name": "股市行情",
            "news": [
                {
                    "content": "上证指数今日收盘上涨2.1%，报收3156点。创业板指数大涨3.5%，两市成交额突破1万亿元。",
                    "source": "国内"
                },
                {
                    "content": "纳斯达克指数收跌1.8%，科技股普遍下跌。苹果股价下跌2.3%，特斯拉跌幅达4.1%。",
                    "source": "彭博社"
                }
            ]
        },
        {
            "name": "国际贸易",
            "news": [
                {
                    "content": "中美贸易额前11个月达到6820亿美元，同比下降13.9%。其中中国对美出口4890亿美元，进口1930亿美元。",
                    "source": "国内"
                }
            ]
        },
        {
            "name": "房地产市场",
            "news": [
                {
                    "content": "全国70个大中城市新建商品住宅价格环比下降0.3%。一线城市房价环比持平，二三线城市分别下降0.4%和0.3%。",
                    "source": "国内"
                },
                {
                    "content": "恒大集团宣布完成债务重组方案，涉及债务总额约3000亿元。公司股票将于下周一复牌交易。",
                    "source": "华尔街日报"
                }
            ]
        }
    ]
    
    for i, scenario in enumerate(scenarios, 1):
        print(f"\n{Fore.YELLOW}📊 场景 {i}: {scenario['name']}")
        print("-" * 40)
        
        news_list = scenario['news']
        
        # 显示原始新闻
        print(f"{Fore.CYAN}📰 原始新闻 ({len(news_list)}条):")
        for j, news in enumerate(news_list, 1):
            print(f"  {j}. [{news['source']}] {news['content'][:80]}...")
        
        # 生成传统标题
        sources = list(set([news.get('source', '未知') for news in news_list]))
        traditional_title = f"快讯汇总 ({len(news_list)}条)"
        if len(sources) <= 3:
            traditional_title += f" - {'/'.join(sources)}"
        
        # 生成 AI 标题
        try:
            ai_title = summarizer.generate_batch_title(news_list)
            final_ai_title = f"{ai_title} ({len(news_list)}条)"
            
            print(f"\n{Fore.WHITE}📝 传统标题: {traditional_title}")
            print(f"{Fore.GREEN}🤖 AI 生成标题: {final_ai_title}")
            
            # 分析改进效果
            print(f"\n{Fore.MAGENTA}📈 改进效果:")
            if "具体数字" in ai_title or any(char.isdigit() for char in ai_title):
                print(f"  ✅ 包含具体数字信息")
            if any(keyword in ai_title for keyword in ["央行", "美联储", "腾讯", "阿里", "恒大", "上证", "纳斯达克"]):
                print(f"  ✅ 包含具体机构/公司名称")
            if any(keyword in ai_title for keyword in ["降准", "加息", "营收", "上涨", "下跌", "重组"]):
                print(f"  ✅ 包含具体事件/行动")
            
        except Exception as e:
            print(f"{Fore.RED}❌ AI 标题生成失败: {e}")
    
    print(f"\n{Fore.GREEN}🎉 测试完成！")
    print(f"\n{Fore.CYAN}💡 优化效果总结:")
    print(f"  • 🎯 标题更加具体，包含关键数字和事实")
    print(f"  • 🏢 明确标识涉及的机构和公司")
    print(f"  • 📊 突出具体的行动和变化")
    print(f"  • 👀 让读者一眼就能了解新闻要点")

if __name__ == "__main__":
    test_specific_scenarios()
