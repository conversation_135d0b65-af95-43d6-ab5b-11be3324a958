#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试分号分隔不同新闻内容的效果
"""

from news_summarizer import NewsSummarizer
from colorama import init, Fore, Style

# 初始化colorama
init(autoreset=True)

def test_semicolon_separation():
    """测试分号分隔效果"""
    print(f"{Fore.GREEN}🎯 测试分号分隔不同新闻内容的效果")
    print("=" * 60)
    
    # 初始化
    summarizer = NewsSummarizer()
    
    # 测试不同类型新闻组合
    test_scenarios = [
        {
            "name": "完全不同领域的新闻",
            "news": [
                {
                    "content": "中国人民银行今日宣布下调存款准备金率0.5个百分点，释放流动性约1万亿元。",
                    "source": "国内"
                },
                {
                    "content": "苹果公司发布第四季度财报，营收894亿美元，同比下降1%。iPhone销量为4780万部。",
                    "source": "彭博社"
                },
                {
                    "content": "欧盟宣布对中国电动汽车征收临时反补贴税，税率为17.4%-38.1%。",
                    "source": "路透社"
                }
            ]
        },
        {
            "name": "相关但不同的金融新闻",
            "news": [
                {
                    "content": "美联储宣布加息25个基点，将联邦基金利率上调至5.25%-5.5%区间。",
                    "source": "路透社"
                },
                {
                    "content": "欧洲央行维持主要再融资利率在4.5%不变，但暗示可能进一步加息。",
                    "source": "华尔街日报"
                }
            ]
        },
        {
            "name": "不同公司的财报新闻",
            "news": [
                {
                    "content": "腾讯控股发布第三季度财报，营收1546亿元，同比增长10%。游戏业务收入达到459亿元。",
                    "source": "彭博社"
                },
                {
                    "content": "阿里巴巴集团公布季度业绩，云计算业务收入276亿元，同比增长2%。",
                    "source": "华尔街日报"
                },
                {
                    "content": "字节跳动2023年营收突破1000亿美元，TikTok贡献超过60%收入。",
                    "source": "科技日报"
                }
            ]
        },
        {
            "name": "不同市场的股市新闻",
            "news": [
                {
                    "content": "上证指数今日收盘上涨2.1%，报收3156点。创业板指数大涨3.5%。",
                    "source": "国内"
                },
                {
                    "content": "纳斯达克指数收跌1.8%，科技股普遍下跌。苹果股价下跌2.3%。",
                    "source": "彭博社"
                },
                {
                    "content": "日经225指数收涨0.8%，汽车股领涨。丰田汽车上涨3.2%。",
                    "source": "路透社"
                }
            ]
        }
    ]
    
    for i, scenario in enumerate(test_scenarios, 1):
        print(f"\n{Fore.YELLOW}📊 测试场景 {i}: {scenario['name']}")
        print("-" * 50)
        
        news_list = scenario['news']
        
        # 显示新闻内容
        print(f"{Fore.CYAN}📰 新闻内容 ({len(news_list)}条):")
        for j, news in enumerate(news_list, 1):
            print(f"  {j}. [{news['source']}] {news['content'][:60]}...")
        
        # 生成 AI 标题
        try:
            ai_title = summarizer.generate_batch_title(news_list)
            final_title = f"{ai_title} ({len(news_list)}条)"
            
            print(f"\n{Fore.GREEN}🤖 AI 生成标题:")
            print(f"   {final_title}")
            
            # 分析分号使用情况
            semicolon_count = ai_title.count(';')
            comma_count = ai_title.count('、')

            # 检查是否包含中文分号
            chinese_semicolon_count = ai_title.count('；')
            total_semicolon = semicolon_count + chinese_semicolon_count
            
            print(f"\n{Fore.MAGENTA}📈 分隔符分析:")
            print(f"  • 英文分号(;)数量: {semicolon_count}")
            print(f"  • 中文分号(；)数量: {chinese_semicolon_count}")
            print(f"  • 总分号数量: {total_semicolon} - 用于分隔不同新闻内容")
            print(f"  • 顿号(、)数量: {comma_count} - 用于连接相关信息")

            if total_semicolon > 0:
                print(f"  ✅ 正确使用分号分隔不同新闻内容")
                # 按分号分割显示各部分（支持中英文分号）
                parts = ai_title.replace('；', ';').split(';')
                print(f"  📝 分隔后的内容:")
                for k, part in enumerate(parts, 1):
                    print(f"     {k}. {part.strip()}")
            else:
                print(f"  ℹ️ 未使用分号，可能是相关性较高的新闻")
            
        except Exception as e:
            print(f"{Fore.RED}❌ AI 标题生成失败: {e}")
    
    # 总结
    print(f"\n{Fore.GREEN}🎉 测试完成！")
    print(f"\n{Fore.CYAN}💡 分号分隔的优势:")
    print(f"  • 🎯 清晰区分不同新闻内容")
    print(f"  • 📊 避免读者误解为同一条新闻")
    print(f"  • 🔍 便于快速扫描不同主题")
    print(f"  • 📱 提高推送消息的可读性")
    
    print(f"\n{Fore.YELLOW}📋 使用规则:")
    print(f"  • 分号(;): 分隔完全不同的新闻内容")
    print(f"  • 顿号(、): 连接同一新闻内的相关信息")
    print(f"  • 逗号(,): 用于数据和描述的自然连接")

if __name__ == "__main__":
    test_semicolon_separation()
