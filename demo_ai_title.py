#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI 标题生成功能演示
展示新闻推送系统的 AI 标题生成能力
"""

from news_summarizer import NewsSummarizer
from colorama import init, Fore, Style
import json

# 初始化colorama
init(autoreset=True)

def demo_title_generation():
    """演示 AI 标题生成功能"""
    print(f"{Fore.GREEN}🤖 新闻推送系统 - AI 标题生成演示")
    print("=" * 60)
    
    # 初始化 AI 标题生成器
    print(f"{Fore.CYAN}🚀 初始化 AI 标题生成器...")
    try:
        summarizer = NewsSummarizer()
        print(f"{Fore.GREEN}✅ 初始化成功，使用模型: {summarizer.model}")
    except Exception as e:
        print(f"{Fore.RED}❌ 初始化失败: {e}")
        return
    
    # 演示数据集
    demo_datasets = [
        {
            "name": "金融市场新闻",
            "news": [
                {
                    "content": "美联储宣布维持基准利率不变，符合市场预期。美联储主席鲍威尔表示，当前经济数据支持维持现有货币政策立场。",
                    "source": "路透社"
                },
                {
                    "content": "中国央行今日进行1000亿元逆回购操作，利率维持在2.0%不变。这是央行连续第三个交易日进行逆回购操作。",
                    "source": "国内"
                }
            ]
        },
        {
            "name": "科技行业动态",
            "news": [
                {
                    "content": "苹果公司发布最新财报，第四季度营收创历史新高。iPhone销量超出分析师预期，推动股价盘后上涨3%。",
                    "source": "彭博社"
                },
                {
                    "content": "特斯拉宣布在中国新建超级工厂，预计年产能将达到100万辆。这是特斯拉在全球的第六座超级工厂。",
                    "source": "科技日报"
                }
            ]
        },
        {
            "name": "国际政治新闻",
            "news": [
                {
                    "content": "欧盟委员会宣布对某科技巨头启动反垄断调查，涉及其在数字市场的主导地位。这是欧盟今年第三起重大反垄断案件。",
                    "source": "华尔街日报"
                }
            ]
        }
    ]
    
    # 逐个演示
    for i, dataset in enumerate(demo_datasets, 1):
        print(f"\n{Fore.YELLOW}📊 演示 {i}: {dataset['name']}")
        print("-" * 40)
        
        news_list = dataset['news']
        
        # 显示原始新闻
        print(f"{Fore.CYAN}📰 原始新闻 ({len(news_list)}条):")
        for j, news in enumerate(news_list, 1):
            print(f"  {j}. [{news['source']}] {news['content'][:60]}...")
        
        # 生成传统标题
        sources = list(set([news.get('source', '未知') for news in news_list]))
        traditional_title = f"快讯汇总 ({len(news_list)}条)"
        if len(sources) <= 3:
            traditional_title += f" - {'/'.join(sources)}"
        
        print(f"\n{Fore.WHITE}📝 传统标题: {traditional_title}")
        
        # 生成 AI 标题
        try:
            ai_title = summarizer.generate_batch_title(news_list)
            final_ai_title = f"{ai_title} ({len(news_list)}条)"
            print(f"{Fore.GREEN}🤖 AI 生成标题: {final_ai_title}")
            
            # 对比分析
            print(f"\n{Fore.MAGENTA}📈 对比分析:")
            print(f"  • 传统标题: 通用性强，但信息量有限")
            print(f"  • AI 标题: 更具体，突出新闻核心内容")
            
        except Exception as e:
            print(f"{Fore.RED}❌ AI 标题生成失败: {e}")
    
    # 性能测试
    print(f"\n{Fore.YELLOW}⚡ 性能测试")
    print("-" * 40)
    
    import time
    test_news = demo_datasets[0]['news']
    
    start_time = time.time()
    try:
        ai_title = summarizer.generate_batch_title(test_news)
        end_time = time.time()
        
        print(f"{Fore.GREEN}✅ 生成时间: {end_time - start_time:.2f} 秒")
        print(f"{Fore.GREEN}✅ 生成标题: {ai_title}")
        
    except Exception as e:
        print(f"{Fore.RED}❌ 性能测试失败: {e}")
    
    # 总结
    print(f"\n{Fore.GREEN}🎉 演示完成！")
    print(f"\n{Fore.CYAN}💡 AI 标题生成的优势:")
    print(f"  • 🎯 更准确地概括新闻内容")
    print(f"  • 📊 提高推送消息的可读性")
    print(f"  • 🔒 完全本地化处理，保护隐私")
    print(f"  • ⚡ 响应速度快，适合实时应用")
    print(f"  • 🛡️ 具有降级机制，确保系统稳定性")

if __name__ == "__main__":
    demo_title_generation()
